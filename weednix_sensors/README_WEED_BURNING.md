# 🔥 ROS-Integrated Weed Burning System

This document describes the ROS integration for the autonomous weed burning system that detects, tracks, and burns weeds using YOLO detection and servo-controlled laser targeting.

## 📋 Overview

The system has been enhanced to publish real-time weeding statistics via ROS topics, allowing integration with other ROS nodes and monitoring systems.

### Key Features
- **Real-time Status Publishing**: Publishes weeding statistics to `/weeding_status` topic
- **ROS Parameter Configuration**: Configurable via ROS parameters
- **Backward Compatibility**: Maintains all original functionality
- **Monitoring Tools**: Includes status monitoring and testing utilities

## 🚀 Quick Start

### 1. Build the Package
```bash
cd ~/weednix_ws
catkin_make
source devel/setup.bash
```

### 2. Launch the System
```bash
# Basic launch
roslaunch weednix_sensors weed_burning_system.launch

# With custom parameters
roslaunch weednix_sensors weed_burning_system.launch \
    arduino_port:=/dev/ttyUSB0 \
    publish_rate:=5.0 \
    burn_time:=1.5
```

### 3. Monitor Status
```bash
# Option 1: Use the built-in monitor
rosrun weednix_sensors weeding_status_monitor.py

# Option 2: Use the simple test subscriber
rosrun weednix_sensors test_weeding_status.py

# Option 3: Use rostopic directly
rostopic echo /weeding_status
```

## 📡 ROS Topic Interface

### Published Topics

#### `/weeding_status` (weednix_sensors/WeedingStatus)
Real-time weeding system status with the following fields:
- `int32 weeds_detected` - Total number of weeds detected in current session
- `int32 weeds_burned` - Total number of weeds successfully burned
- `time stamp` - Timestamp of the status update

**Publishing Rate**: Configurable (default: 2 Hz)

### Example Message
```yaml
weeds_detected: 15
weeds_burned: 12
stamp: 
  secs: 1640995200
  nsecs: 123456789
```

## ⚙️ Configuration Parameters

### Launch File Parameters
- `model_path`: Path to YOLO model file (default: `best.pt` in scripts directory)
- `arduino_port`: Arduino serial port (default: `/dev/ttyACM0`)
- `publish_rate`: Status publishing rate in Hz (default: `2.0`)
- `burn_time`: Laser activation time in seconds (default: `2.0`)
- `calibration_dir`: Directory containing servo calibration files (default: scripts directory)
- `show_display`: Enable/disable OpenCV display window (default: `true`)

### Runtime Parameters
All parameters can be set via ROS parameter server:
```bash
rosparam set /weed_burning_system/publish_rate 5.0
rosparam set /weed_burning_system/burn_time 1.5
```

## 🛠️ Hardware Requirements

### Required Hardware
- **Camera**: Kinect v1 for weed detection
- **Arduino**: For servo and laser control
- **Servos**: X and Y axis servos (pins D9, D10)
- **Laser**: Burning laser (pin D8)
- **YOLO Model**: Trained weed detection model (`best.pt`)

### Connections
- Servo X: Arduino pin D10
- Servo Y: Arduino pin D9  
- Laser: Arduino pin D8
- Arduino: USB connection (typically `/dev/ttyACM0`)

## 📁 File Structure

```
weednix_sensors/
├── msg/
│   └── WeedingStatus.msg          # Custom ROS message definition
├── scripts/
│   ├── burn_calibrated.py         # Original standalone script
│   ├── burn_calibrated_ros.py     # ROS-integrated version
│   ├── weeding_status_monitor.py  # Status monitoring utility
│   ├── test_weeding_status.py     # Simple test subscriber
│   └── best.pt                    # YOLO model file
├── launch/
│   └── weed_burning_system.launch # Launch file
└── README_WEED_BURNING.md         # This documentation
```

## 🔧 Usage Examples

### Basic Usage
```bash
# Terminal 1: Launch the weed burning system
roslaunch weednix_sensors weed_burning_system.launch

# Terminal 2: Monitor the status
rosrun weednix_sensors weeding_status_monitor.py
```

### Integration with Other Nodes
```python
#!/usr/bin/env python3
import rospy
from weednix_sensors.msg import WeedingStatus

def weeding_callback(msg):
    if msg.weeds_detected > 10:
        rospy.loginfo("High weed density detected!")
    
    efficiency = msg.weeds_burned / msg.weeds_detected * 100
    if efficiency < 80:
        rospy.logwarn(f"Low burning efficiency: {efficiency:.1f}%")

rospy.init_node('weed_analyzer')
rospy.Subscriber('/weeding_status', WeedingStatus, weeding_callback)
rospy.spin()
```

### Custom Launch Configuration
```xml
<launch>
  <include file="$(find weednix_sensors)/launch/weed_burning_system.launch">
    <arg name="publish_rate" value="10.0"/>
    <arg name="burn_time" value="1.0"/>
    <arg name="show_display" value="false"/>
  </include>
  
  <!-- Your additional nodes here -->
</launch>
```

## 🐛 Troubleshooting

### Common Issues

1. **Arduino Connection Failed**
   ```bash
   # Check available ports
   ls /dev/ttyACM* /dev/ttyUSB*
   
   # Update launch file with correct port
   roslaunch weednix_sensors weed_burning_system.launch arduino_port:=/dev/ttyUSB0
   ```

2. **Kinect Not Detected**
   ```bash
   # Install Kinect drivers
   sudo apt-get install freenect
   
   # Test Kinect
   freenect-glview
   ```

3. **YOLO Model Not Found**
   ```bash
   # Ensure model file exists
   ls ~/weednix_ws/src/weednix_sensors/scripts/best.pt
   
   # Or specify custom path
   roslaunch weednix_sensors weed_burning_system.launch model_path:=/path/to/your/model.pt
   ```

4. **No Status Messages**
   ```bash
   # Check if node is running
   rosnode list | grep weed_burning
   
   # Check topic
   rostopic list | grep weeding_status
   
   # Check for errors
   rosnode info /weed_burning_system
   ```

## 📊 Performance Monitoring

### Key Metrics
- **Detection Rate**: Weeds detected per second
- **Burning Rate**: Weeds burned per second  
- **Efficiency**: Percentage of detected weeds successfully burned
- **Session Statistics**: Cumulative counts and averages

### Monitoring Commands
```bash
# Real-time topic monitoring
rostopic hz /weeding_status

# Message content inspection
rostopic echo /weeding_status

# System resource usage
htop
```

## 🔄 Integration with Existing System

The ROS-integrated weed burning system is designed to work alongside the existing weednix robot infrastructure:

- **Compatible with**: All existing launch files and configurations
- **Extends**: Current sensor and navigation capabilities
- **Publishes to**: Standard ROS ecosystem for easy integration
- **Configurable**: Via existing ROS parameter mechanisms

## 📝 Development Notes

### Message Design
The `WeedingStatus` message is kept simple with essential information:
- Integer counters for easy processing
- Timestamp for temporal analysis
- Extensible design for future enhancements

### Code Structure
- **Modular Design**: Clear separation between detection, control, and ROS communication
- **Error Handling**: Robust error handling for hardware failures
- **Logging**: Comprehensive ROS logging for debugging
- **Resource Management**: Proper cleanup and resource management

## 🚀 Future Enhancements

Potential improvements for future versions:
- GPS coordinates for weed locations
- Weed classification and size estimation
- Battery and system health monitoring
- Integration with field mapping systems
- Machine learning performance metrics
